// Sample applicant data based on the handwritten layout format
export const applicantsData = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0101",
    position: "Software Engineer",
    experience: "3 years",
    education: "BS Computer Science",
    applicationDate: "2024-01-15",
    status: "Under Review",
    location: "New York, NY",
    salary: "$85,000",
    availability: "Immediate",
    skills: "React, Node.js, Python",
    files: [
      { name: "<PERSON><PERSON>_Resume.pdf", type: "resume", size: "245 KB" },
      { name: "<PERSON><PERSON><PERSON>_CoverLetter.pdf", type: "cover_letter", size: "156 KB" },
      { name: "<PERSON><PERSON><PERSON>_Portfolio.pdf", type: "portfolio", size: "2.1 MB" }
    ]
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0102",
    position: "Frontend Developer",
    experience: "2 years",
    education: "BS Web Development",
    applicationDate: "2024-01-18",
    status: "Interview Scheduled",
    location: "San Francisco, CA",
    salary: "$75,000",
    availability: "2 weeks notice",
    skills: "Vue.js, CSS, JavaScript",
    files: [
      { name: "<PERSON>_<PERSON>_<PERSON>sume.pdf", type: "resume", size: "198 KB" },
      { name: "Bob_Smith_CoverLetter.pdf", type: "cover_letter", size: "142 KB" }
    ]
  },
  {
    id: 3,
    name: "Carol <PERSON>",
    email: "<EMAIL>",
    phone: "******-0103",
    position: "Backend Developer",
    experience: "5 years",
    education: "MS Computer Science",
    applicationDate: "2024-01-20",
    status: "Pending",
    location: "Austin, TX",
    salary: "$95,000",
    availability: "1 month notice",
    skills: "Java, Spring, MySQL",
    files: [
      { name: "Carol_Davis_Resume.pdf", type: "resume", size: "267 KB" },
      { name: "Carol_Davis_CoverLetter.pdf", type: "cover_letter", size: "178 KB" },
      { name: "Carol_Davis_References.pdf", type: "references", size: "89 KB" }
    ]
  },
  {
    id: 4,
    name: "David Wilson",
    email: "<EMAIL>",
    phone: "******-0104",
    position: "Full Stack Developer",
    experience: "4 years",
    education: "BS Software Engineering",
    applicationDate: "2024-01-22",
    status: "Rejected",
    location: "Seattle, WA",
    salary: "$88,000",
    availability: "Immediate",
    skills: "React, Express, MongoDB",
    files: [
      { name: "David_Wilson_Resume.pdf", type: "resume", size: "223 KB" },
      { name: "David_Wilson_CoverLetter.pdf", type: "cover_letter", size: "134 KB" }
    ]
  },
  {
    id: 5,
    name: "Eva Brown",
    email: "<EMAIL>",
    phone: "******-0105",
    position: "UI/UX Designer",
    experience: "3 years",
    education: "BA Graphic Design",
    applicationDate: "2024-01-25",
    status: "Hired",
    location: "Los Angeles, CA",
    salary: "$72,000",
    availability: "2 weeks notice",
    skills: "Figma, Adobe XD, Sketch",
    files: [
      { name: "Eva_Brown_Resume.pdf", type: "resume", size: "189 KB" },
      { name: "Eva_Brown_Portfolio.pdf", type: "portfolio", size: "5.2 MB" },
      { name: "Eva_Brown_CoverLetter.pdf", type: "cover_letter", size: "145 KB" }
    ]
  },
  {
    id: 6,
    name: "Frank Miller",
    email: "<EMAIL>",
    phone: "******-0106",
    position: "DevOps Engineer",
    experience: "6 years",
    education: "BS Information Technology",
    applicationDate: "2024-01-28",
    status: "Under Review",
    location: "Chicago, IL",
    salary: "$105,000",
    availability: "1 month notice",
    skills: "Docker, Kubernetes, AWS",
    files: [
      { name: "Frank_Miller_Resume.pdf", type: "resume", size: "298 KB" },
      { name: "Frank_Miller_CoverLetter.pdf", type: "cover_letter", size: "167 KB" },
      { name: "Frank_Miller_Certifications.pdf", type: "certifications", size: "234 KB" }
    ]
  },
  {
    id: 7,
    name: "Grace Lee",
    email: "<EMAIL>",
    phone: "******-0107",
    position: "Data Scientist",
    experience: "4 years",
    education: "MS Data Science",
    applicationDate: "2024-02-01",
    status: "Interview Scheduled",
    location: "Boston, MA",
    salary: "$98,000",
    availability: "3 weeks notice",
    skills: "Python, R, Machine Learning",
    files: [
      { name: "Grace_Lee_Resume.pdf", type: "resume", size: "276 KB" },
      { name: "Grace_Lee_CoverLetter.pdf", type: "cover_letter", size: "158 KB" },
      { name: "Grace_Lee_Research_Papers.pdf", type: "portfolio", size: "1.8 MB" }
    ]
  },
  {
    id: 8,
    name: "Henry Taylor",
    email: "<EMAIL>",
    phone: "******-0108",
    position: "Mobile Developer",
    experience: "3 years",
    education: "BS Computer Engineering",
    applicationDate: "2024-02-03",
    status: "Pending",
    location: "Miami, FL",
    salary: "$82,000",
    availability: "Immediate",
    skills: "React Native, Swift, Kotlin",
    files: [
      { name: "Henry_Taylor_Resume.pdf", type: "resume", size: "212 KB" },
      { name: "Henry_Taylor_CoverLetter.pdf", type: "cover_letter", size: "139 KB" },
      { name: "Henry_Taylor_App_Screenshots.pdf", type: "portfolio", size: "3.4 MB" }
    ]
  }
];

// Status options for filtering
export const statusOptions = [
  "All",
  "Pending",
  "Under Review", 
  "Interview Scheduled",
  "Hired",
  "Rejected"
];

// Position options for filtering
export const positionOptions = [
  "All",
  "Software Engineer",
  "Frontend Developer",
  "Backend Developer",
  "Full Stack Developer",
  "UI/UX Designer",
  "DevOps Engineer",
  "Data Scientist",
  "Mobile Developer"
];
