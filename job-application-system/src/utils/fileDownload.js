// File download utility with error handling
export const downloadFile = async (fileName, applicantName) => {
  try {
    // First try to find the file in the parent directory (sample-application-files)
    let response = await fetch(`../sample-application-files/${fileName}`, { method: 'HEAD' });
    let filePath = `../sample-application-files/${fileName}`;

    if (!response.ok) {
      // If not found, try the original sample-files directory
      response = await fetch(`/sample-files/${fileName}`, { method: 'HEAD' });
      filePath = `/sample-files/${fileName}`;
    }

    if (!response.ok) {
      // If file doesn't exist in either location, download a sample file instead
      console.warn(`File ${fileName} not found, downloading sample file instead`);
      downloadSampleFile(fileName);
      return;
    }

    // Create download link
    const link = document.createElement('a');
    link.href = filePath;
    link.download = fileName;
    link.style.display = 'none';

    // Add to DOM, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    showNotification(`Successfully downloaded ${fileName}`, 'success');

  } catch (error) {
    console.error('Download error:', error);
    showNotification(`Failed to download ${fileName}`, 'error');

    // Fallback to sample file
    downloadSampleFile(fileName);
  }
};

// Download a sample file when the actual file is not available
const downloadSampleFile = (originalFileName) => {
  const link = document.createElement('a');
  link.href = '/sample-files/sample-resume.pdf';
  link.download = originalFileName;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  showNotification(`Downloaded sample file as ${originalFileName}`, 'info');
};

// Simple notification system
const showNotification = (message, type = 'info') => {
  // Remove existing notifications
  const existingNotifications = document.querySelectorAll('.download-notification');
  existingNotifications.forEach(notification => notification.remove());
  
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `download-notification notification-${type}`;
  notification.textContent = message;
  
  // Style the notification
  Object.assign(notification.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    padding: '12px 20px',
    borderRadius: '6px',
    color: 'white',
    fontWeight: '500',
    zIndex: '10000',
    maxWidth: '300px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    animation: 'slideInRight 0.3s ease-out'
  });
  
  // Set background color based on type
  switch (type) {
    case 'success':
      notification.style.backgroundColor = '#28a745';
      break;
    case 'error':
      notification.style.backgroundColor = '#dc3545';
      break;
    case 'info':
      notification.style.backgroundColor = '#17a2b8';
      break;
    default:
      notification.style.backgroundColor = '#6c757d';
  }
  
  // Add animation styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }
  `;
  
  if (!document.querySelector('#notification-styles')) {
    style.id = 'notification-styles';
    document.head.appendChild(style);
  }
  
  // Add to DOM
  document.body.appendChild(notification);
  
  // Auto remove after 3 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 300);
  }, 3000);
};

// Utility to get file type icon
export const getFileTypeIcon = (fileType) => {
  const icons = {
    resume: '📄',
    cover_letter: '📝',
    portfolio: '🎨',
    references: '👥',
    certifications: '🏆',
    transcript: '📊'
  };
  
  return icons[fileType] || '📎';
};

// Utility to format file size
export const formatFileSize = (sizeString) => {
  // If already formatted, return as is
  if (sizeString.includes('KB') || sizeString.includes('MB') || sizeString.includes('GB')) {
    return sizeString;
  }
  
  // Convert bytes to readable format
  const bytes = parseInt(sizeString);
  if (isNaN(bytes)) return sizeString;
  
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
};
