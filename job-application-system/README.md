# Job Application Management System

A modern web application for managing job applications with admin authentication and file download capabilities.

## Features

### 🔐 Admin Authentication
- Secure login page with hardcoded credentials
- Session persistence using localStorage
- Protected dashboard access

### 📊 Job Applicants Dashboard
- Clean, responsive table layout displaying applicant information
- Real-time search and filtering capabilities
- Sortable columns for better data organization
- Status badges with color coding
- Statistics overview cards

### 📁 File Management
- Download functionality for applicant attachments
- Support for multiple file types (resume, cover letter, portfolio, etc.)
- File type icons and size information
- Error handling with fallback to sample files
- Toast notifications for download status

### 📱 Responsive Design
- Mobile-first approach
- Optimized for desktop, tablet, and mobile devices
- Modern UI with gradient backgrounds and smooth animations

## Demo Credentials

- **Username:** admin
- **Password:** 1234

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd job-application-system
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

## Project Structure

```
src/
├── components/
│   ├── Login.jsx          # Login page component
│   ├── Login.css          # Login page styles
│   ├── Dashboard.jsx      # Main dashboard component
│   └── Dashboard.css      # Dashboard styles
├── contexts/
│   └── AuthContext.jsx    # Authentication context
├── data/
│   └── applicantsData.js  # Sample applicant data
├── utils/
│   └── fileDownload.js    # File download utilities
├── App.jsx                # Main app component
├── App.css                # Global app styles
├── index.css              # Global CSS reset and base styles
└── main.jsx               # App entry point
```

## Sample Data

The application includes 8 sample applicants with various:
- Positions (Software Engineer, Frontend Developer, etc.)
- Application statuses (Pending, Under Review, Hired, etc.)
- File attachments (resumes, cover letters, portfolios)
- Contact information and experience details

## Technologies Used

- **React 18** - UI library
- **Vite** - Build tool and development server
- **CSS3** - Styling with modern features
- **JavaScript ES6+** - Modern JavaScript features

## Features in Detail

### Authentication System
- Simple but secure login mechanism
- Persistent sessions across browser refreshes
- Automatic logout functionality

### Dashboard Features
- **Search:** Real-time search across name, email, and position
- **Filtering:** Filter by status and position
- **Sorting:** Click column headers to sort data
- **Statistics:** Overview cards showing key metrics
- **File Downloads:** One-click download for applicant files

### Responsive Design
- Breakpoints for mobile (480px), tablet (768px), and desktop
- Flexible grid layouts
- Touch-friendly interface elements
- Optimized typography and spacing

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is for demonstration purposes.
